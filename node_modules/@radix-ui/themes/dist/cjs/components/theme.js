"use strict";"use client";var M=Object.create;var R=Object.defineProperty;var O=Object.getOwnPropertyDescriptor;var V=Object.getOwnPropertyNames;var W=Object.getPrototypeOf,q=Object.prototype.hasOwnProperty;var z=(o,r)=>{for(var a in r)R(o,a,{get:r[a],enumerable:!0})},E=(o,r,a,s)=>{if(r&&typeof r=="object"||typeof r=="function")for(let t of V(r))!q.call(o,t)&&t!==a&&R(o,t,{get:()=>r[t],enumerable:!(s=O(r,t))||s.enumerable});return o};var G=(o,r,a)=>(a=o!=null?M(W(o)):{},E(r||!o||!o.__esModule?R(a,"default",{value:o,enumerable:!0}):a,o)),F=o=>E(R({},"__esModule",{value:!0}),o);var J={};z(J,{Theme:()=>S,ThemeContext:()=>u,useThemeContext:()=>H});module.exports=F(J);var e=G(require("react")),w=G(require("classnames")),h=require("radix-ui"),N=require("../helpers/get-matching-gray-color.js"),n=require("./theme.props.js");const d=()=>{},u=e.createContext(void 0);function H(){const o=e.useContext(u);if(o===void 0)throw new Error("`useThemeContext` must be used within a `Theme`");return o}const S=e.forwardRef((o,r)=>e.useContext(u)===void 0?e.createElement(h.Tooltip.Provider,{delayDuration:200},e.createElement(h.Direction.Provider,{dir:"ltr"},e.createElement(D,{...o,ref:r}))):e.createElement(I,{...o,ref:r}));S.displayName="Theme";const D=e.forwardRef((o,r)=>{const{appearance:a=n.themePropDefs.appearance.default,accentColor:s=n.themePropDefs.accentColor.default,grayColor:t=n.themePropDefs.grayColor.default,panelBackground:p=n.themePropDefs.panelBackground.default,radius:c=n.themePropDefs.radius.default,scaling:l=n.themePropDefs.scaling.default,hasBackground:i=n.themePropDefs.hasBackground.default,...g}=o,[C,m]=e.useState(a);e.useEffect(()=>m(a),[a]);const[v,f]=e.useState(s);e.useEffect(()=>f(s),[s]);const[k,T]=e.useState(t);e.useEffect(()=>T(t),[t]);const[B,P]=e.useState(p);e.useEffect(()=>P(p),[p]);const[x,b]=e.useState(c);e.useEffect(()=>b(c),[c]);const[y,A]=e.useState(l);return e.useEffect(()=>A(l),[l]),e.createElement(I,{...g,ref:r,isRoot:!0,hasBackground:i,appearance:C,accentColor:v,grayColor:k,panelBackground:B,radius:x,scaling:y,onAppearanceChange:m,onAccentColorChange:f,onGrayColorChange:T,onPanelBackgroundChange:P,onRadiusChange:b,onScalingChange:A})});D.displayName="ThemeRoot";const I=e.forwardRef((o,r)=>{const a=e.useContext(u),{asChild:s,isRoot:t,hasBackground:p,appearance:c=a?.appearance??n.themePropDefs.appearance.default,accentColor:l=a?.accentColor??n.themePropDefs.accentColor.default,grayColor:i=a?.resolvedGrayColor??n.themePropDefs.grayColor.default,panelBackground:g=a?.panelBackground??n.themePropDefs.panelBackground.default,radius:C=a?.radius??n.themePropDefs.radius.default,scaling:m=a?.scaling??n.themePropDefs.scaling.default,onAppearanceChange:v=d,onAccentColorChange:f=d,onGrayColorChange:k=d,onPanelBackgroundChange:T=d,onRadiusChange:B=d,onScalingChange:P=d,...x}=o,b=s?h.Slot.Root:"div",y=i==="auto"?(0,N.getMatchingGrayColor)(l):i,A=o.appearance==="light"||o.appearance==="dark",j=p===void 0?t||A:p;return e.createElement(u.Provider,{value:e.useMemo(()=>({appearance:c,accentColor:l,grayColor:i,resolvedGrayColor:y,panelBackground:g,radius:C,scaling:m,onAppearanceChange:v,onAccentColorChange:f,onGrayColorChange:k,onPanelBackgroundChange:T,onRadiusChange:B,onScalingChange:P}),[c,l,i,y,g,C,m,v,f,k,T,B,P])},e.createElement(b,{"data-is-root-theme":t?"true":"false","data-accent-color":l,"data-gray-color":y,"data-has-background":j?"true":"false","data-panel-background":g,"data-radius":C,"data-scaling":m,ref:r,...x,className:(0,w.default)("radix-themes",{light:c==="light",dark:c==="dark"},x.className)}))});I.displayName="ThemeImpl";
//# sourceMappingURL=theme.js.map
