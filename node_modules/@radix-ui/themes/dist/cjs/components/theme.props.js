"use strict";var t=Object.defineProperty;var f=Object.getOwnPropertyDescriptor;var c=Object.getOwnPropertyNames;var i=Object.prototype.hasOwnProperty;var m=(o,e)=>{for(var a in e)t(o,a,{get:e[a],enumerable:!0})},d=(o,e,a,n)=>{if(e&&typeof e=="object"||typeof e=="function")for(let r of c(e))!i.call(o,r)&&r!==a&&t(o,r,{get:()=>e[r],enumerable:!(n=f(e,r))||n.enumerable});return o};var y=o=>d(t({},"__esModule",{value:!0}),o);var h={};m(h,{themePropDefs:()=>l});module.exports=y(h);var s=require("../props/as-child.prop.js"),p=require("../props/color.prop.js"),u=require("../props/radius.prop.js");const g=["inherit","light","dark"],P=["solid","translucent"],D=["90%","95%","100%","105%","110%"],l={...s.asChildPropDef,hasBackground:{type:"boolean",default:!0},appearance:{type:"enum",values:g,default:"inherit"},accentColor:{type:"enum",values:p.accentColors,default:"indigo"},grayColor:{type:"enum",values:p.grayColors,default:"auto"},panelBackground:{type:"enum",values:P,default:"translucent"},radius:{type:"enum",values:u.radii,default:"medium"},scaling:{type:"enum",values:D,default:"100%"}};
//# sourceMappingURL=theme.props.js.map
