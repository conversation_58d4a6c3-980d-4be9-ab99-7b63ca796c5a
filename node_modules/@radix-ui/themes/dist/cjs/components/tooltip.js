"use strict";var w=Object.create;var s=Object.defineProperty;var D=Object.getOwnPropertyDescriptor;var O=Object.getOwnPropertyNames;var W=Object.getPrototypeOf,E=Object.prototype.hasOwnProperty;var b=(o,t)=>{for(var e in t)s(o,e,{get:t[e],enumerable:!0})},m=(o,t,e,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let p of O(t))!E.call(o,p)&&p!==e&&s(o,p,{get:()=>t[p],enumerable:!(n=D(t,p))||n.enumerable});return o};var a=(o,t,e)=>(e=o!=null?w(W(o)):{},m(t||!o||!o.__esModule?s(e,"default",{value:o,enumerable:!0}):e,o)),j=o=>m(s({},"__esModule",{value:!0}),o);var A={};b(A,{Tooltip:()=>l});module.exports=j(A);var i=a(require("react")),f=a(require("classnames")),r=require("radix-ui"),P=require("./text.js"),T=require("./theme.js"),c=require("../helpers/extract-props.js"),d=require("./tooltip.props.js");const l=i.forwardRef((o,t)=>{const{children:e,className:n,open:p,defaultOpen:C,onOpenChange:y,delayDuration:h,disableHoverableContent:v,content:u,container:R,forceMount:x,...N}=(0,c.extractProps)(o,d.tooltipPropDefs),g={open:p,defaultOpen:C,onOpenChange:y,delayDuration:h,disableHoverableContent:v};return i.createElement(r.Tooltip.Root,{...g},i.createElement(r.Tooltip.Trigger,{asChild:!0},e),i.createElement(r.Tooltip.Portal,{container:R,forceMount:x},i.createElement(T.Theme,{asChild:!0},i.createElement(r.Tooltip.Content,{sideOffset:4,collisionPadding:10,...N,asChild:!1,ref:t,className:(0,f.default)("rt-TooltipContent",n)},i.createElement(P.Text,{as:"p",className:"rt-TooltipText",size:"1"},u),i.createElement(r.Tooltip.Arrow,{className:"rt-TooltipArrow"})))))});l.displayName="Tooltip";
//# sourceMappingURL=tooltip.js.map
