"use strict";var i=Object.defineProperty;var d=Object.getOwnPropertyDescriptor;var n=Object.getOwnPropertyNames;var P=Object.prototype.hasOwnProperty;var h=(e,t)=>{for(var p in t)i(e,p,{get:t[p],enumerable:!0})},D=(e,t,p,f)=>{if(t&&typeof t=="object"||typeof t=="function")for(let o of n(t))!P.call(e,o)&&o!==p&&i(e,o,{get:()=>t[o],enumerable:!(f=d(t,o))||f.enumerable});return e};var m=e=>D(i({},"__esModule",{value:!0}),e);var a={};h(a,{tooltipPropDefs:()=>s});module.exports=m(a);var r=require("../props/width.props.js");const s={content:{type:"ReactNode",required:!0},width:r.widthPropDefs.width,minWidth:r.widthPropDefs.minWidth,maxWidth:{...r.widthPropDefs.maxWidth,default:"360px"}};
//# sourceMappingURL=tooltip.props.js.map
