"use strict";"use client";var q=Object.create;var b=Object.defineProperty;var J=Object.getOwnPropertyDescriptor;var Q=Object.getOwnPropertyNames;var Y=Object.getPrototypeOf,_=Object.prototype.hasOwnProperty;var R=(n,l)=>{for(var i in l)b(n,i,{get:l[i],enumerable:!0})},E=(n,l,i,p)=>{if(l&&typeof l=="object"||typeof l=="function")for(let r of Q(l))!_.call(n,r)&&r!==i&&b(n,r,{get:()=>l[r],enumerable:!(p=J(l,r))||p.enumerable});return n};var e9=(n,l,i)=>(i=n!=null?q(Y(n)):{},E(l||!n||!n.__esModule?b(i,"default",{value:n,enumerable:!0}):i,n)),t9=n=>E(b({},"__esModule",{value:!0}),n);var a9={};R(a9,{ThemePanel:()=>H});module.exports=t9(a9);var e=e9(require("react")),S=require("radix-ui/internal"),o=require("../index.js"),h=require("./theme.js"),N=require("../helpers/inert.js"),z=require("../helpers/get-matching-gray-color.js"),d=require("./theme.props.js");const H=e.forwardRef(({defaultOpen:n=!0,...l},i)=>{const[p,r]=e.useState(n);return e.createElement(K,{...l,ref:i,open:p,onOpenChange:r})});H.displayName="ThemePanel";const K=e.forwardRef((n,l)=>{const{open:i,onOpenChange:p,onAppearanceChange:r,...y}=n,G=(0,h.useThemeContext)(),{appearance:c,onAppearanceChange:I,accentColor:u,onAccentColorChange:$,grayColor:g,onGrayColorChange:F,panelBackground:L,onPanelBackgroundChange:O,radius:T,onRadiusChange:D,scaling:w,onScalingChange:j}=G,A=r!==void 0,V=(0,S.useCallbackRef)(r),x=e.useCallback(t=>{const a=o9();if(c!=="inherit"){I(t);return}A?V(t):(Z(t),n9(t)),a()},[c,I,A,V]),k=(0,z.getMatchingGrayColor)(u),B=g==="auto"?k:g,[W,P]=e.useState("idle");async function U(){const t={appearance:c===d.themePropDefs.appearance.default?void 0:c,accentColor:u===d.themePropDefs.accentColor.default?void 0:u,grayColor:g===d.themePropDefs.grayColor.default?void 0:g,panelBackground:L===d.themePropDefs.panelBackground.default?void 0:L,radius:T===d.themePropDefs.radius.default?void 0:T,scaling:w===d.themePropDefs.scaling.default?void 0:w},a=Object.keys(t).filter(s=>t[s]!==void 0).map(s=>`${s}="${t[s]}"`).join(" "),m=a?`<Theme ${a}>`:"<Theme>";P("copying"),await navigator.clipboard.writeText(m),P("copied"),setTimeout(()=>P("idle"),2e3)}const[M,Z]=e.useState(c==="inherit"?null:c),v=`
      [contenteditable],
      [role="combobox"],
      [role="listbox"],
      [role="menu"],
      input:not([type="radio"], [type="checkbox"]),
      select,
      textarea
    `;return e.useEffect(()=>{function t(a){const m=a.altKey||a.ctrlKey||a.shiftKey||a.metaKey,s=document.activeElement?.closest(v);a.key?.toUpperCase()==="T"&&!m&&!s&&p(!i)}return document.addEventListener("keydown",t),()=>document.removeEventListener("keydown",t)},[p,i,v]),e.useEffect(()=>{function t(a){const m=a.altKey||a.ctrlKey||a.shiftKey||a.metaKey,s=document.activeElement?.closest(v);a.key?.toUpperCase()==="D"&&!m&&!s&&x(M==="light"?"dark":"light")}return document.addEventListener("keydown",t),()=>document.removeEventListener("keydown",t)},[x,M,v]),e.useEffect(()=>{const t=document.documentElement,a=document.body;function m(){const f=t.classList.contains("dark")||t.classList.contains("dark-theme")||a.classList.contains("dark")||a.classList.contains("dark-theme");Z(c==="inherit"?f?"dark":"light":c)}const s=new MutationObserver(function(f){f.forEach(function(X){X.attributeName==="class"&&m()})});return m(),c==="inherit"&&(s.observe(t,{attributes:!0}),s.observe(a,{attributes:!0})),()=>s.disconnect()},[c]),e.createElement(h.Theme,{asChild:!0,radius:"medium",scaling:"100%"},e.createElement(o.Flex,{direction:"column",position:"fixed",top:"0",right:"0",mr:"4",mt:"4",inert:i?void 0:N.inert,...y,ref:l,style:{zIndex:9999,overflow:"hidden",maxHeight:"calc(100vh - var(--space-4) - var(--space-4))",borderRadius:"var(--radius-4)",backgroundColor:"var(--color-panel-solid)",transformOrigin:"top center",transitionProperty:"transform, box-shadow",transitionDuration:"200ms",transitionTimingFunction:i?"ease-out":"ease-in",transform:i?"none":"translateX(105%)",boxShadow:i?"var(--shadow-5)":"var(--shadow-2)",...n.style}},e.createElement(o.ScrollArea,null,e.createElement(o.Box,{flexGrow:"1",p:"5",position:"relative"},e.createElement(o.Box,{position:"absolute",top:"0",right:"0",m:"2"},e.createElement(o.Tooltip,{content:"Press T to show/hide the Theme Panel",side:"bottom",sideOffset:6},e.createElement(o.Kbd,{asChild:!0,size:"3",tabIndex:0,className:"rt-ThemePanelShortcut"},e.createElement("button",{onClick:()=>p(!i)},"T")))),e.createElement(o.Heading,{size:"5",trim:"both",as:"h3",mb:"5"},"Theme"),e.createElement(o.Text,{id:"accent-color-title",as:"p",size:"2",weight:"medium",mt:"5"},"Accent color"),e.createElement(o.Grid,{columns:"10",gap:"2",mt:"3",role:"group","aria-labelledby":"accent-color-title"},d.themePropDefs.accentColor.values.map(t=>e.createElement("label",{key:t,className:"rt-ThemePanelSwatch",style:{backgroundColor:`var(--${t}-9)`}},e.createElement(o.Tooltip,{content:`${C(t)}${u==="gray"&&B!=="gray"?` (${C(B)})`:""}`},e.createElement("input",{className:"rt-ThemePanelSwatchInput",type:"radio",name:"accentColor",value:t,checked:u===t,onChange:a=>$(a.target.value)}))))),e.createElement(o.Flex,{asChild:!0,align:"center",justify:"between"},e.createElement(o.Text,{as:"p",id:"gray-color-title",size:"2",weight:"medium",mt:"5"},"Gray color")),e.createElement(o.Grid,{columns:"10",gap:"2",mt:"3",role:"group","aria-labelledby":"gray-color-title"},d.themePropDefs.grayColor.values.map(t=>e.createElement(o.Flex,{key:t,asChild:!0,align:"center",justify:"center"},e.createElement("label",{className:"rt-ThemePanelSwatch",style:{backgroundColor:t==="auto"?`var(--${k}-9)`:t==="gray"?"var(--gray-9)":`var(--${t}-9)`,filter:t==="gray"?"saturate(0)":void 0}},e.createElement(o.Tooltip,{content:`${C(t)}${t==="auto"?` (${C(k)})`:""}`},e.createElement("input",{className:"rt-ThemePanelSwatchInput",type:"radio",name:"grayColor",value:t,checked:g===t,onChange:a=>F(a.target.value)})))))),e.createElement(o.Text,{id:"appearance-title",as:"p",size:"2",weight:"medium",mt:"5"},"Appearance"),e.createElement(o.Grid,{columns:"2",gap:"2",mt:"3",role:"group","aria-labelledby":"appearance-title"},["light","dark"].map(t=>e.createElement("label",{key:t,className:"rt-ThemePanelRadioCard"},e.createElement("input",{className:"rt-ThemePanelRadioCardInput",type:"radio",name:"appearance",value:t,checked:M===t,onChange:a=>x(a.target.value)}),e.createElement(o.Flex,{align:"center",justify:"center",height:"32px",gap:"2"},t==="light"?e.createElement("svg",{width:"15",height:"15",viewBox:"0 0 15 15",fill:"none",xmlns:"http://www.w3.org/2000/svg",style:{margin:"0 -1px"}},e.createElement("path",{d:"M7.5 0C7.77614 0 8 0.223858 8 0.5V2.5C8 2.77614 7.77614 3 7.5 3C7.22386 3 7 2.77614 7 2.5V0.5C7 0.223858 7.22386 0 7.5 0ZM2.1967 2.1967C2.39196 2.00144 2.70854 2.00144 2.90381 2.1967L4.31802 3.61091C4.51328 3.80617 4.51328 4.12276 4.31802 4.31802C4.12276 4.51328 3.80617 4.51328 3.61091 4.31802L2.1967 2.90381C2.00144 2.70854 2.00144 2.39196 2.1967 2.1967ZM0.5 7C0.223858 7 0 7.22386 0 7.5C0 7.77614 0.223858 8 0.5 8H2.5C2.77614 8 3 7.77614 3 7.5C3 7.22386 2.77614 7 2.5 7H0.5ZM2.1967 12.8033C2.00144 12.608 2.00144 12.2915 2.1967 12.0962L3.61091 10.682C3.80617 10.4867 4.12276 10.4867 4.31802 10.682C4.51328 10.8772 4.51328 11.1938 4.31802 11.3891L2.90381 12.8033C2.70854 12.9986 2.39196 12.9986 2.1967 12.8033ZM12.5 7C12.2239 7 12 7.22386 12 7.5C12 7.77614 12.2239 8 12.5 8H14.5C14.7761 8 15 7.77614 15 7.5C15 7.22386 14.7761 7 14.5 7H12.5ZM10.682 4.31802C10.4867 4.12276 10.4867 3.80617 10.682 3.61091L12.0962 2.1967C12.2915 2.00144 12.608 2.00144 12.8033 2.1967C12.9986 2.39196 12.9986 2.70854 12.8033 2.90381L11.3891 4.31802C11.1938 4.51328 10.8772 4.51328 10.682 4.31802ZM8 12.5C8 12.2239 7.77614 12 7.5 12C7.22386 12 7 12.2239 7 12.5V14.5C7 14.7761 7.22386 15 7.5 15C7.77614 15 8 14.7761 8 14.5V12.5ZM10.682 10.682C10.8772 10.4867 11.1938 10.4867 11.3891 10.682L12.8033 12.0962C12.9986 12.2915 12.9986 12.608 12.8033 12.8033C12.608 12.9986 12.2915 12.9986 12.0962 12.8033L10.682 11.3891C10.4867 11.1938 10.4867 10.8772 10.682 10.682ZM5.5 7.5C5.5 6.39543 6.39543 5.5 7.5 5.5C8.60457 5.5 9.5 6.39543 9.5 7.5C9.5 8.60457 8.60457 9.5 7.5 9.5C6.39543 9.5 5.5 8.60457 5.5 7.5ZM7.5 4.5C5.84315 4.5 4.5 5.84315 4.5 7.5C4.5 9.15685 5.84315 10.5 7.5 10.5C9.15685 10.5 10.5 9.15685 10.5 7.5C10.5 5.84315 9.15685 4.5 7.5 4.5Z",fill:"currentColor",fillRule:"evenodd",clipRule:"evenodd"})):e.createElement("svg",{width:"15",height:"15",viewBox:"0 0 15 15",fill:"none",xmlns:"http://www.w3.org/2000/svg",style:{margin:"0 -1px"}},e.createElement("path",{d:"M2.89998 0.499976C2.89998 0.279062 2.72089 0.0999756 2.49998 0.0999756C2.27906 0.0999756 2.09998 0.279062 2.09998 0.499976V1.09998H1.49998C1.27906 1.09998 1.09998 1.27906 1.09998 1.49998C1.09998 1.72089 1.27906 1.89998 1.49998 1.89998H2.09998V2.49998C2.09998 2.72089 2.27906 2.89998 2.49998 2.89998C2.72089 2.89998 2.89998 2.72089 2.89998 2.49998V1.89998H3.49998C3.72089 1.89998 3.89998 1.72089 3.89998 1.49998C3.89998 1.27906 3.72089 1.09998 3.49998 1.09998H2.89998V0.499976ZM5.89998 3.49998C5.89998 3.27906 5.72089 3.09998 5.49998 3.09998C5.27906 3.09998 5.09998 3.27906 5.09998 3.49998V4.09998H4.49998C4.27906 4.09998 4.09998 4.27906 4.09998 4.49998C4.09998 4.72089 4.27906 4.89998 4.49998 4.89998H5.09998V5.49998C5.09998 5.72089 5.27906 5.89998 5.49998 5.89998C5.72089 5.89998 5.89998 5.72089 5.89998 5.49998V4.89998H6.49998C6.72089 4.89998 6.89998 4.72089 6.89998 4.49998C6.89998 4.27906 6.72089 4.09998 6.49998 4.09998H5.89998V3.49998ZM1.89998 6.49998C1.89998 6.27906 1.72089 6.09998 1.49998 6.09998C1.27906 6.09998 1.09998 6.27906 1.09998 6.49998V7.09998H0.499976C0.279062 7.09998 0.0999756 7.27906 0.0999756 7.49998C0.0999756 7.72089 0.279062 7.89998 0.499976 7.89998H1.09998V8.49998C1.09998 8.72089 1.27906 8.89997 1.49998 8.89997C1.72089 8.89997 1.89998 8.72089 1.89998 8.49998V7.89998H2.49998C2.72089 7.89998 2.89998 7.72089 2.89998 7.49998C2.89998 7.27906 2.72089 7.09998 2.49998 7.09998H1.89998V6.49998ZM8.54406 0.98184L8.24618 0.941586C8.03275 0.917676 7.90692 1.1655 8.02936 1.34194C8.17013 1.54479 8.29981 1.75592 8.41754 1.97445C8.91878 2.90485 9.20322 3.96932 9.20322 5.10022C9.20322 8.37201 6.82247 11.0878 3.69887 11.6097C3.45736 11.65 3.20988 11.6772 2.96008 11.6906C2.74563 11.702 2.62729 11.9535 2.77721 12.1072C2.84551 12.1773 2.91535 12.2458 2.98667 12.3128L3.05883 12.3795L3.31883 12.6045L3.50684 12.7532L3.62796 12.8433L3.81491 12.9742L3.99079 13.089C4.11175 13.1651 4.23536 13.2375 4.36157 13.3059L4.62496 13.4412L4.88553 13.5607L5.18837 13.6828L5.43169 13.7686C5.56564 13.8128 5.70149 13.8529 5.83857 13.8885C5.94262 13.9155 6.04767 13.9401 6.15405 13.9622C6.27993 13.9883 6.40713 14.0109 6.53544 14.0298L6.85241 14.0685L7.11934 14.0892C7.24637 14.0965 7.37436 14.1002 7.50322 14.1002C11.1483 14.1002 14.1032 11.1453 14.1032 7.50023C14.1032 7.25044 14.0893 7.00389 14.0623 6.76131L14.0255 6.48407C13.991 6.26083 13.9453 6.04129 13.8891 5.82642C13.8213 5.56709 13.7382 5.31398 13.6409 5.06881L13.5279 4.80132L13.4507 4.63542L13.3766 4.48666C13.2178 4.17773 13.0353 3.88295 12.8312 3.60423L12.6782 3.40352L12.4793 3.16432L12.3157 2.98361L12.1961 2.85951L12.0355 2.70246L11.8134 2.50184L11.4925 2.24191L11.2483 2.06498L10.9562 1.87446L10.6346 1.68894L10.3073 1.52378L10.1938 1.47176L9.95488 1.3706L9.67791 1.2669L9.42566 1.1846L9.10075 1.09489L8.83599 1.03486L8.54406 0.98184ZM10.4032 5.30023C10.4032 4.27588 10.2002 3.29829 9.83244 2.40604C11.7623 3.28995 13.1032 5.23862 13.1032 7.50023C13.1032 10.593 10.596 13.1002 7.50322 13.1002C6.63646 13.1002 5.81597 12.9036 5.08355 12.5522C6.5419 12.0941 7.81081 11.2082 8.74322 10.0416C8.87963 10.2284 9.10028 10.3497 9.34928 10.3497C9.76349 10.3497 10.0993 10.0139 10.0993 9.59971C10.0993 9.24256 9.84965 8.94373 9.51535 8.86816C9.57741 8.75165 9.63653 8.63334 9.6926 8.51332C9.88358 8.63163 10.1088 8.69993 10.35 8.69993C11.0403 8.69993 11.6 8.14028 11.6 7.44993C11.6 6.75976 11.0406 6.20024 10.3505 6.19993C10.3853 5.90487 10.4032 5.60464 10.4032 5.30023Z",fill:"currentColor",fillRule:"evenodd",clipRule:"evenodd"})),e.createElement(o.Text,{size:"1",weight:"medium"},C(t)))))),e.createElement(o.Text,{id:"radius-title",as:"p",size:"2",weight:"medium",mt:"5"},"Radius"),e.createElement(o.Grid,{columns:"5",gap:"2",mt:"3",role:"group","aria-labelledby":"radius-title"},d.themePropDefs.radius.values.map(t=>e.createElement(o.Flex,{key:t,direction:"column",align:"center"},e.createElement("label",{className:"rt-ThemePanelRadioCard"},e.createElement("input",{className:"rt-ThemePanelRadioCardInput",type:"radio",name:"radius",id:`theme-panel-radius-${t}`,value:t,checked:T===t,onChange:a=>D(a.target.value)}),e.createElement(h.Theme,{asChild:!0,radius:t},e.createElement(o.Box,{m:"3",width:"32px",height:"32px",style:{borderTopLeftRadius:t==="full"?"80%":"var(--radius-5)",backgroundImage:"linear-gradient(to bottom right, var(--accent-3), var(--accent-4))",borderTop:"2px solid var(--accent-a8)",borderLeft:"2px solid var(--accent-a8)"}}))),e.createElement(o.Box,{asChild:!0,pt:"2"},e.createElement(o.Text,{asChild:!0,size:"1",color:"gray"},e.createElement("label",{htmlFor:`theme-panel-radius-${t}`},C(t))))))),e.createElement(o.Text,{id:"scaling-title",as:"p",size:"2",weight:"medium",mt:"5"},"Scaling"),e.createElement(o.Grid,{columns:"5",gap:"2",mt:"3",role:"group","aria-labelledby":"scaling-title"},d.themePropDefs.scaling.values.map(t=>e.createElement("label",{key:t,className:"rt-ThemePanelRadioCard"},e.createElement("input",{className:"rt-ThemePanelRadioCardInput",type:"radio",name:"scaling",value:t,checked:w===t,onChange:a=>j(a.target.value)}),e.createElement(o.Flex,{align:"center",justify:"center",height:"32px"},e.createElement(h.Theme,{asChild:!0,scaling:t},e.createElement(o.Flex,{align:"center",justify:"center"},e.createElement(o.Text,{size:"1",weight:"medium"},C(t)))))))),e.createElement(o.Flex,{mt:"5",align:"center",gap:"2"},e.createElement(o.Text,{id:"panel-background-title",as:"p",size:"2",weight:"medium"},"Panel background"),e.createElement(o.Popover.Root,null,e.createElement(o.Popover.Trigger,null,e.createElement(o.IconButton,{size:"1",variant:"ghost",color:"gray"},e.createElement(o.AccessibleIcon,{label:"Learn more about panel background options"},e.createElement("svg",{width:"15",height:"15",viewBox:"0 0 15 15",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"},e.createElement("path",{d:"M7.49991 0.876892C3.84222 0.876892 0.877075 3.84204 0.877075 7.49972C0.877075 11.1574 3.84222 14.1226 7.49991 14.1226C11.1576 14.1226 14.1227 11.1574 14.1227 7.49972C14.1227 3.84204 11.1576 0.876892 7.49991 0.876892ZM1.82707 7.49972C1.82707 4.36671 4.36689 1.82689 7.49991 1.82689C10.6329 1.82689 13.1727 4.36671 13.1727 7.49972C13.1727 10.6327 10.6329 13.1726 7.49991 13.1726C4.36689 13.1726 1.82707 10.6327 1.82707 7.49972ZM8.24992 4.49999C8.24992 4.9142 7.91413 5.24999 7.49992 5.24999C7.08571 5.24999 6.74992 4.9142 6.74992 4.49999C6.74992 4.08577 7.08571 3.74999 7.49992 3.74999C7.91413 3.74999 8.24992 4.08577 8.24992 4.49999ZM6.00003 5.99999H6.50003H7.50003C7.77618 5.99999 8.00003 6.22384 8.00003 6.49999V9.99999H8.50003H9.00003V11H8.50003H7.50003H6.50003H6.00003V9.99999H6.50003H7.00003V6.99999H6.50003H6.00003V5.99999Z",fillRule:"evenodd",clipRule:"evenodd"}))))),e.createElement(o.Popover.Content,{size:"1",style:{maxWidth:220},side:"top",align:"center"},e.createElement(o.Text,{as:"p",size:"2"},"Whether Card and Table panels are translucent, showing some of\xA0the background behind them.")))),e.createElement(o.Grid,{columns:"2",gap:"2",mt:"3",role:"group","aria-labelledby":"panel-background-title"},d.themePropDefs.panelBackground.values.map(t=>e.createElement("label",{key:t,className:"rt-ThemePanelRadioCard"},e.createElement("input",{className:"rt-ThemePanelRadioCardInput",type:"radio",name:"panelBackground",value:t,checked:L===t,onChange:a=>O(a.target.value)}),e.createElement(o.Flex,{align:"center",justify:"center",height:"32px",gap:"2"},t==="solid"?e.createElement("svg",{width:"15",height:"15",viewBox:"0 0 15 15",fill:"none",xmlns:"http://www.w3.org/2000/svg",style:{margin:"0 -2px"}},e.createElement("path",{d:"M0.877075 7.49988C0.877075 3.84219 3.84222 0.877045 7.49991 0.877045C11.1576 0.877045 14.1227 3.84219 14.1227 7.49988C14.1227 11.1575 11.1576 14.1227 7.49991 14.1227C3.84222 14.1227 0.877075 11.1575 0.877075 7.49988ZM7.49991 1.82704C4.36689 1.82704 1.82708 4.36686 1.82708 7.49988C1.82708 10.6329 4.36689 13.1727 7.49991 13.1727C10.6329 13.1727 13.1727 10.6329 13.1727 7.49988C13.1727 4.36686 10.6329 1.82704 7.49991 1.82704Z",fill:"currentColor",fillRule:"evenodd",clipRule:"evenodd"})):e.createElement("svg",{width:"15",height:"15",viewBox:"0 0 15 15",fill:"none",xmlns:"http://www.w3.org/2000/svg",style:{margin:"0 -2px"}},e.createElement("path",{opacity:".05",d:"M6.78296 13.376C8.73904 9.95284 8.73904 5.04719 6.78296 1.62405L7.21708 1.37598C9.261 4.95283 9.261 10.0472 7.21708 13.624L6.78296 13.376Z",fill:"currentColor",fillRule:"evenodd",clipRule:"evenodd"}),e.createElement("path",{opacity:".1",d:"M7.28204 13.4775C9.23929 9.99523 9.23929 5.00475 7.28204 1.52248L7.71791 1.2775C9.76067 4.9119 9.76067 10.0881 7.71791 13.7225L7.28204 13.4775Z",fill:"currentColor",fillRule:"evenodd",clipRule:"evenodd"}),e.createElement("path",{opacity:".15",d:"M7.82098 13.5064C9.72502 9.99523 9.72636 5.01411 7.82492 1.50084L8.26465 1.26285C10.2465 4.92466 10.2451 10.085 8.26052 13.7448L7.82098 13.5064Z",fill:"currentColor",fillRule:"evenodd",clipRule:"evenodd"}),e.createElement("path",{opacity:".2",d:"M8.41284 13.429C10.1952 9.92842 10.1957 5.07537 8.41435 1.57402L8.85999 1.34729C10.7139 4.99113 10.7133 10.0128 8.85841 13.6559L8.41284 13.429Z",fill:"currentColor",fillRule:"evenodd",clipRule:"evenodd"}),e.createElement("path",{opacity:".25",d:"M9.02441 13.2956C10.6567 9.8379 10.6586 5.17715 9.03005 1.71656L9.48245 1.50366C11.1745 5.09919 11.1726 9.91629 9.47657 13.5091L9.02441 13.2956Z",fill:"currentColor",fillRule:"evenodd",clipRule:"evenodd"}),e.createElement("path",{opacity:".3",d:"M9.66809 13.0655C11.1097 9.69572 11.1107 5.3121 9.67088 1.94095L10.1307 1.74457C11.6241 5.24121 11.6231 9.76683 10.1278 13.2622L9.66809 13.0655Z",fill:"currentColor",fillRule:"evenodd",clipRule:"evenodd"}),e.createElement("path",{opacity:".35",d:"M10.331 12.7456C11.5551 9.52073 11.5564 5.49103 10.3347 2.26444L10.8024 2.0874C12.0672 5.42815 12.0659 9.58394 10.7985 12.9231L10.331 12.7456Z",fill:"currentColor",fillRule:"evenodd",clipRule:"evenodd"}),e.createElement("path",{opacity:".4",d:"M11.0155 12.2986C11.9938 9.29744 11.9948 5.71296 11.0184 2.71067L11.4939 2.55603C12.503 5.6589 12.502 9.35178 11.4909 12.4535L11.0155 12.2986Z",fill:"currentColor",fillRule:"evenodd",clipRule:"evenodd"}),e.createElement("path",{opacity:".45",d:"M11.7214 11.668C12.4254 9.01303 12.4262 5.99691 11.7237 3.34116L12.2071 3.21329C12.9318 5.95292 12.931 9.05728 12.2047 11.7961L11.7214 11.668Z",fill:"currentColor",fillRule:"evenodd",clipRule:"evenodd"}),e.createElement("path",{opacity:".5",d:"M12.4432 10.752C12.8524 8.63762 12.8523 6.36089 12.4429 4.2466L12.9338 4.15155C13.3553 6.32861 13.3554 8.66985 12.9341 10.847L12.4432 10.752Z",fill:"currentColor",fillRule:"evenodd",clipRule:"evenodd"}),e.createElement("path",{d:"M0.877075 7.49988C0.877075 3.84219 3.84222 0.877045 7.49991 0.877045C11.1576 0.877045 14.1227 3.84219 14.1227 7.49988C14.1227 11.1575 11.1576 14.1227 7.49991 14.1227C3.84222 14.1227 0.877075 11.1575 0.877075 7.49988ZM7.49991 1.82704C4.36689 1.82704 1.82708 4.36686 1.82708 7.49988C1.82708 10.6329 4.36689 13.1727 7.49991 13.1727C10.6329 13.1727 13.1727 10.6329 13.1727 7.49988C13.1727 4.36686 10.6329 1.82704 7.49991 1.82704Z",fill:"currentColor",fillRule:"evenodd",clipRule:"evenodd"})),e.createElement(o.Text,{size:"1",weight:"medium"},C(t)))))),e.createElement(o.Button,{mt:"5",style:{width:"100%"},onClick:U},W==="copied"?"Copied":"Copy Theme")))))});K.displayName="ThemePanelImpl";function o9(){const n=document.createElement("style");return n.appendChild(document.createTextNode("*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(n),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(n)},1)}}function C(n){return n.charAt(0).toUpperCase()+n.slice(1)}function n9(n){const l=document.documentElement,i=l.classList.contains("light-theme"),p=l.classList.contains("dark-theme"),r=l.classList.contains("light"),y=l.classList.contains("dark");(i||p)&&(l.classList.remove("light-theme","dark-theme"),l.style.colorScheme=n,l.classList.add(`${n}-theme`)),(r||y)&&(l.classList.remove("light","dark"),l.style.colorScheme=n,l.classList.add(n)),!i&&!p&&!r&&!y&&(l.style.colorScheme=n,l.classList.add(n))}
//# sourceMappingURL=theme-panel.js.map
