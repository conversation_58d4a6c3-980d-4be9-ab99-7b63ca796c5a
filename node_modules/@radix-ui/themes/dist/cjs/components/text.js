"use strict";var v=Object.create;var s=Object.defineProperty;var R=Object.getOwnPropertyDescriptor;var h=Object.getOwnPropertyNames;var C=Object.getPrototypeOf,D=Object.prototype.hasOwnProperty;var g=(o,e)=>{for(var p in e)s(o,p,{get:e[p],enumerable:!0})},n=(o,e,p,r)=>{if(e&&typeof e=="object"||typeof e=="function")for(let t of h(e))!D.call(o,t)&&t!==p&&s(o,t,{get:()=>e[t],enumerable:!(r=R(e,t))||r.enumerable});return o};var P=(o,e,p)=>(p=o!=null?v(C(o)):{},n(e||!o||!o.__esModule?s(p,"default",{value:o,enumerable:!0}):p,o)),u=o=>n(s({},"__esModule",{value:!0}),o);var W={};g(W,{Text:()=>a});module.exports=u(W);var m=P(require("react")),x=P(require("classnames")),i=require("radix-ui"),T=require("../helpers/extract-props.js"),f=require("../props/margin.props.js"),l=require("./text.props.js");const a=m.forwardRef((o,e)=>{const{children:p,className:r,asChild:t,as:y="span",color:d,...c}=(0,T.extractProps)(o,l.textPropDefs,f.marginPropDefs);return m.createElement(i.Slot.Root,{"data-accent-color":d,...c,ref:e,className:(0,x.default)("rt-Text",r)},t?p:m.createElement(y,null,p))});a.displayName="Text";
//# sourceMappingURL=text.js.map
