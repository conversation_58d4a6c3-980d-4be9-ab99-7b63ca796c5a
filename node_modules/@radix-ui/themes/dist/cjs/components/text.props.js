"use strict";var t=Object.defineProperty;var c=Object.getOwnPropertyDescriptor;var d=Object.getOwnPropertyNames;var y=Object.prototype.hasOwnProperty;var z=(r,o)=>{for(var p in o)t(r,p,{get:o[p],enumerable:!0})},g=(r,o,p,s)=>{if(o&&typeof o=="object"||typeof o=="function")for(let e of d(o))!y.call(r,e)&&e!==p&&t(r,e,{get:()=>o[e],enumerable:!(s=c(o,e))||s.enumerable});return r};var h=r=>g(t({},"__esModule",{value:!0}),r);var b={};z(b,{textPropDefs:()=>u});module.exports=h(b);var f=require("../props/as-child.prop.js"),i=require("../props/color.prop.js"),m=require("../props/high-contrast.prop.js"),a=require("../props/leading-trim.prop.js"),n=require("../props/text-align.prop.js"),D=require("../props/text-wrap.prop.js"),P=require("../props/truncate.prop.js"),l=require("../props/weight.prop.js");const v=["span","div","label","p"],x=["1","2","3","4","5","6","7","8","9"],u={as:{type:"enum",values:v,default:"span"},...f.asChildPropDef,size:{type:"enum",className:"rt-r-size",values:x,responsive:!0},...l.weightPropDef,...n.textAlignPropDef,...a.leadingTrimPropDef,...P.truncatePropDef,...D.textWrapPropDef,...i.colorPropDef,...m.highContrastPropDef};
//# sourceMappingURL=text.props.js.map
