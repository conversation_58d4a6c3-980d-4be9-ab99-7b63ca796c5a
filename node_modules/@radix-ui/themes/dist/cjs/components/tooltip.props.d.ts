import { widthPropDefs } from '../props/width.props.js';
import type { GetPropDefTypes } from '../props/prop-def.js';
declare const tooltipPropDefs: {
    content: {
        type: "ReactNode";
        required: true;
    };
    width: {
        type: "string";
        className: string;
        customProperties: "--width"[];
        responsive: true;
    };
    minWidth: {
        type: "string";
        className: string;
        customProperties: "--min-width"[];
        responsive: true;
    };
    maxWidth: {
        default: string;
        type: "string";
        className: string;
        customProperties: "--max-width"[];
        responsive: true;
    };
};
type TooltipOwnProps = GetPropDefTypes<typeof tooltipPropDefs & typeof widthPropDefs>;
export { tooltipPropDefs };
export type { TooltipOwnProps };
//# sourceMappingURL=tooltip.props.d.ts.map