"use strict";var l=Object.defineProperty;var r=Object.getOwnPropertyDescriptor;var n=Object.getOwnPropertyNames;var a=Object.prototype.hasOwnProperty;var p=(o,i)=>{for(var e in i)l(o,e,{get:i[e],enumerable:!0})},y=(o,i,e,s)=>{if(i&&typeof i=="object"||typeof i=="function")for(let d of n(i))!a.call(o,d)&&d!==e&&l(o,d,{get:()=>i[d],enumerable:!(s=r(i,d))||s.enumerable});return o};var u=o=>y(l({},"__esModule",{value:!0}),o);var m={};p(m,{Root:()=>V,VisuallyHidden:()=>H});module.exports=u(m);var t=require("radix-ui");const H=t.VisuallyHidden.Root,V=t.VisuallyHidden.Root;
//# sourceMappingURL=visually-hidden.js.map
