{"version": 3, "sources": ["../../../src/components/tooltip.tsx"], "sourcesContent": ["import * as React from 'react';\nimport classNames from 'classnames';\nimport { Tooltip as TooltipPrimitive } from 'radix-ui';\n\nimport { Text } from './text.js';\nimport { Theme } from './theme.js';\nimport { extractProps } from '../helpers/extract-props.js';\nimport { tooltipPropDefs } from './tooltip.props.js';\n\nimport type { ComponentPropsWithout, RemovedProps } from '../helpers/component-props.js';\nimport type { GetPropDefTypes } from '../props/prop-def.js';\n\ntype TooltipElement = React.ElementRef<typeof TooltipPrimitive.Content>;\ntype TooltipOwnProps = GetPropDefTypes<typeof tooltipPropDefs>;\ninterface TooltipProps\n  extends React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Root>,\n    ComponentPropsWithout<typeof TooltipPrimitive.Content, RemovedProps | 'content'>,\n    TooltipOwnProps {\n  content: React.ReactNode;\n  container?: React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Portal>['container'];\n}\nconst Tooltip = React.forwardRef<TooltipElement, TooltipProps>((props, forwardedRef) => {\n  const {\n    children,\n    className,\n    open,\n    defaultOpen,\n    onOpenChange,\n    delayDuration,\n    disableHoverableContent,\n    content,\n    container,\n    forceMount,\n    ...tooltipContentProps\n  } = extractProps(props, tooltipPropDefs);\n  const rootProps = { open, defaultOpen, onOpenChange, delayDuration, disableHoverableContent };\n  return (\n    <TooltipPrimitive.Root {...rootProps}>\n      <TooltipPrimitive.Trigger asChild>{children}</TooltipPrimitive.Trigger>\n      <TooltipPrimitive.Portal container={container} forceMount={forceMount}>\n        <Theme asChild>\n          <TooltipPrimitive.Content\n            sideOffset={4}\n            collisionPadding={10}\n            {...tooltipContentProps}\n            asChild={false}\n            ref={forwardedRef}\n            className={classNames('rt-TooltipContent', className)}\n          >\n            <Text as=\"p\" className=\"rt-TooltipText\" size=\"1\">\n              {content}\n            </Text>\n            <TooltipPrimitive.Arrow className=\"rt-TooltipArrow\" />\n          </TooltipPrimitive.Content>\n        </Theme>\n      </TooltipPrimitive.Portal>\n    </TooltipPrimitive.Root>\n  );\n});\nTooltip.displayName = 'Tooltip';\n\nexport { Tooltip };\nexport type { TooltipProps };\n"], "mappings": "0jBAAA,IAAAA,EAAA,GAAAC,EAAAD,EAAA,aAAAE,IAAA,eAAAC,EAAAH,GAAA,IAAAI,EAAuB,oBACvBC,EAAuB,yBACvBC,EAA4C,oBAE5CC,EAAqB,qBACrBC,EAAsB,sBACtBC,EAA6B,uCAC7BC,EAAgC,8BAchC,MAAMR,EAAUE,EAAM,WAAyC,CAACO,EAAOC,IAAiB,CACtF,KAAM,CACJ,SAAAC,EACA,UAAAC,EACA,KAAAC,EACA,YAAAC,EACA,aAAAC,EACA,cAAAC,EACA,wBAAAC,EACA,QAAAC,EACA,UAAAC,EACA,WAAAC,EACA,GAAGC,CACL,KAAI,gBAAaZ,EAAO,iBAAe,EACjCa,EAAY,CAAE,KAAAT,EAAM,YAAAC,EAAa,aAAAC,EAAc,cAAAC,EAAe,wBAAAC,CAAwB,EAC5F,OACEf,EAAA,cAAC,EAAAqB,QAAiB,KAAjB,CAAuB,GAAGD,GACzBpB,EAAA,cAAC,EAAAqB,QAAiB,QAAjB,CAAyB,QAAO,IAAEZ,CAAS,EAC5CT,EAAA,cAAC,EAAAqB,QAAiB,OAAjB,CAAwB,UAAWJ,EAAW,WAAYC,GACzDlB,EAAA,cAAC,SAAM,QAAO,IACZA,EAAA,cAAC,EAAAqB,QAAiB,QAAjB,CACC,WAAY,EACZ,iBAAkB,GACjB,GAAGF,EACJ,QAAS,GACT,IAAKX,EACL,aAAW,EAAAc,SAAW,oBAAqBZ,CAAS,GAEpDV,EAAA,cAAC,QAAK,GAAG,IAAI,UAAU,iBAAiB,KAAK,KAC1CgB,CACH,EACAhB,EAAA,cAAC,EAAAqB,QAAiB,MAAjB,CAAuB,UAAU,kBAAkB,CACtD,CACF,CACF,CACF,CAEJ,CAAC,EACDvB,EAAQ,YAAc", "names": ["tooltip_exports", "__export", "<PERSON><PERSON><PERSON>", "__toCommonJS", "React", "import_classnames", "import_radix_ui", "import_text", "import_theme", "import_extract_props", "import_tooltip_props", "props", "forwardedRef", "children", "className", "open", "defaultOpen", "onOpenChange", "delayDuration", "disableHover<PERSON><PERSON><PERSON>nt", "content", "container", "forceMount", "tooltipContentProps", "rootProps", "TooltipPrimitive", "classNames"]}