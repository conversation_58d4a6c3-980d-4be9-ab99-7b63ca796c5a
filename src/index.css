* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: linear-gradient(135deg, #fef7f0 0%, #fff5f5 100%);
  color: #1a1a1a;
  min-height: 100vh;
}

#root {
  min-height: 100vh;
  display: flex;
}

/* Professional sidebar with inner shadows */
.sidebar {
  width: 240px;
  background: linear-gradient(180deg, #ffffff 0%, #fafbfc 100%);
  border-right: 1px solid #f0f0f0;
  box-shadow: inset -1px 0 0 rgba(255, 255, 255, 0.8),
              inset 0 1px 0 rgba(255, 255, 255, 0.8),
              1px 0 3px rgba(0, 0, 0, 0.02);
  padding: 0;
  display: flex;
  flex-direction: column;
  position: relative;
  height: 100vh;
  overflow-y: auto;
}

.main-content {
  flex: 1;
  padding: 32px;
  background: #f8f9fa;
  overflow-y: auto;
  height: 100vh;
  min-width: 0;
}

.dashboard-content {
  display: grid;
  grid-template-columns: 1fr 420px;
  gap: 32px;
  width: 100%;
  height: calc(100vh - 64px);
}

@media (min-width: 1400px) {
  .main-content {
    padding: 40px;
  }

  .dashboard-content {
    grid-template-columns: 1fr 480px;
    gap: 40px;
  }
}

@media (min-width: 1600px) {
  .dashboard-content {
    grid-template-columns: 1fr 520px;
    gap: 48px;
  }
}

@media (min-width: 1920px) {
  .main-content {
    padding: 48px;
  }

  .dashboard-content {
    grid-template-columns: 1fr 600px;
    gap: 56px;
  }
}

.dashboard-main {
  display: flex;
  flex-direction: column;
  gap: 24px;
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04), 0 1px 3px rgba(0, 0, 0, 0.06);
  border: 1px solid #e8e8e8;
  height: 100%;
  overflow-y: auto;
}

.ai-chat-section {
  background: white;
  border-radius: 16px;
  border: 2px solid #ff5722;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08), 0 2px 8px rgba(255, 87, 34, 0.1);
  overflow: hidden;
  height: 100%;
}

/* ChatGPT-style chat container */
.ai-chat-container {
  background: #ffffff;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.ai-chat-header {
  padding: 24px 32px;
  border-bottom: 1px solid #f0f0f0;
  background: linear-gradient(135deg, #ffffff 0%, #fff8f6 100%);
  flex-shrink: 0;
}

.ai-chat-content {
  flex: 1;
  padding: 40px 32px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  background: linear-gradient(135deg, #fafbfc 0%, #fff8f6 100%);
  min-height: 300px;
}

.ai-chat-input-area {
  padding: 24px 32px;
  border-top: 1px solid #f0f0f0;
  background: white;
  flex-shrink: 0;
}

/* Compact chat input */
.chat-input-container {
  position: relative;
  background: #f7f7f8;
  border: 1px solid #d1d5db;
  border-radius: 12px;
  padding: 16px 16px 16px 16px;
  min-height: 80px;
  transition: all 0.2s ease;
}

.chat-input-container:focus-within {
  border-color: #ff5722;
  box-shadow: 0 0 0 3px rgba(255, 87, 34, 0.1);
}

.chat-input {
  width: 100%;
  border: none;
  background: transparent;
  outline: none;
  font-size: 14px;
  line-height: 1.5;
  resize: none;
  min-height: 48px;
  max-height: 120px;
  color: #1a202c;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  padding-right: 40px;
}

.chat-input::placeholder {
  color: #9ca3af;
  font-weight: 400;
}

.chat-placeholder-text {
  position: absolute;
  top: 16px;
  left: 16px;
  font-size: 12px;
  color: #9ca3af;
  pointer-events: none;
}

.chat-send-button {
  position: absolute;
  bottom: 12px;
  right: 12px;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  border: none;
  background: rgba(255, 87, 34, 0.1);
  color: #ff5722;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.chat-send-button:hover {
  background: rgba(255, 87, 34, 0.2);
  transform: scale(1.05);
}

.chat-send-button:disabled {
  background: rgba(209, 213, 219, 0.5);
  color: #9ca3af;
  cursor: not-allowed;
  transform: none;
}

.suggested-question {
  padding: 10px 14px;
  background: white;
  border: 1px dashed #ff6b47;
  border-radius: 6px;
  font-size: 12px;
  color: #666;
  cursor: pointer;
  transition: all 0.15s ease;
  text-align: left;
  line-height: 1.4;
}

.suggested-question:hover {
  background: #fff5f5;
  border-color: #ff5722;
  color: #ff5722;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(255, 107, 71, 0.12);
}

/* Clean metrics cards with grey border */
.metrics-card {
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  padding: 16px;
  transition: all 0.2s ease;
}

.metrics-card:hover {
  border-color: #cbd5e0;
  transform: translateY(-1px);
}

/* Compact typography */
.dashboard-header {
  margin-bottom: 24px;
}

.dashboard-title {
  font-size: 22px;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 4px;
  letter-spacing: -0.025em;
}

.dashboard-subtitle {
  font-size: 14px;
  color: #718096;
  font-weight: 400;
  line-height: 1.5;
}

.metrics-label {
  font-size: 11px;
  font-weight: 600;
  color: #718096;
  text-transform: uppercase;
  letter-spacing: 0.02em;
  margin-bottom: 6px;
}

.metrics-value {
  font-size: 24px;
  font-weight: 700;
  color: #1a202c;
  line-height: 1;
  margin-bottom: 6px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 4px;
}

.section-subtitle {
  font-size: 13px;
  color: #718096;
  margin-bottom: 16px;
}



.data-table-card {
  background: #ffffff;
  border-radius: 12px;
  border: 1px solid #e8e8e8;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.04), 0 1px 2px rgba(0, 0, 0, 0.06);
  padding: 0;
  flex: 1;
}

/* Compact button styles */
.classic-button {
  background: linear-gradient(145deg, #ffffff 0%, #f8f8f8 100%);
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  padding: 8px 12px;
  font-size: 13px;
  font-weight: 500;
  color: #333;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.8),
              0 1px 3px rgba(0, 0, 0, 0.1);
  min-height: 32px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  min-width: auto;
  width: auto;
}

.classic-button:hover {
  background: linear-gradient(145deg, #f8f8f8 0%, #f0f0f0 100%);
  border-color: #d0d0d0;
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.9),
              0 2px 6px rgba(0, 0, 0, 0.15);
}

.classic-button:active {
  background: linear-gradient(145deg, #f0f0f0 0%, #e8e8e8 100%);
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
  transform: translateY(1px);
}

.classic-button.primary {
  background: linear-gradient(145deg, #ff6b47 0%, #ff5722 100%);
  border-color: #ff5722;
  color: white;
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2),
              0 2px 8px rgba(255, 107, 71, 0.3);
}

.classic-button.primary:hover {
  background: linear-gradient(145deg, #ff5722 0%, #e64a19 100%);
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.3),
              0 4px 12px rgba(255, 107, 71, 0.4);
}

/* Small flat buttons */
.flat-button {
  background: transparent;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 12px;
  font-weight: 500;
  color: #666;
  cursor: pointer;
  transition: all 0.2s ease;
}

.flat-button:hover {
  background: #fff5f5;
  border-color: #ff6b47;
  color: #ff5722;
}

/* Clean sidebar sections */
.sidebar-section {
  padding: 16px 0 12px 0;
}

.sidebar-section-header {
  padding: 0 20px 6px 20px;
  font-size: 11px;
  font-weight: 700;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.8px;
  margin-bottom: 2px;
}

.sidebar-nav {
  padding: 0 12px;
}

.sidebar-item {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 8px 14px;
  margin: 4px 8px;
  border-radius: 7px;
  cursor: pointer;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 14px;
  font-weight: 500;
  color: #475569;
  text-decoration: none;
  border: 1px solid transparent;
  background: linear-gradient(135deg, rgba(148, 163, 184, 0.06) 0%, rgba(148, 163, 184, 0.08) 100%);
  width: calc(100% - 16px);
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', sans-serif;
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.05),
              inset 0 -1px 0 rgba(0, 0, 0, 0.01),
              0 1px 3px rgba(0, 0, 0, 0.02);
  position: relative;
  overflow: hidden;
}

.sidebar-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%, rgba(0, 0, 0, 0.02) 100%);
  opacity: 0;
  transition: opacity 0.25s ease;
}

.sidebar-item:hover {
  background: linear-gradient(135deg, rgba(148, 163, 184, 0.1) 0%, rgba(148, 163, 184, 0.14) 100%);
  color: #334155;
  border-color: rgba(148, 163, 184, 0.12);
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.08),
              inset 0 -1px 0 rgba(0, 0, 0, 0.015),
              0 2px 8px rgba(0, 0, 0, 0.03),
              0 1px 3px rgba(0, 0, 0, 0.02);
  transform: translateY(-0.5px);
}

.sidebar-item:hover::before {
  opacity: 1;
}

.sidebar-item.active {
  background: linear-gradient(135deg, rgba(255, 107, 71, 0.08) 0%, rgba(255, 87, 34, 0.12) 100%);
  color: #ff5722;
  font-weight: 600;
  border-color: rgba(255, 107, 71, 0.2);
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.12),
              inset 0 -1px 0 rgba(255, 107, 71, 0.06),
              inset 2px 0 0 rgba(255, 107, 71, 0.25),
              0 3px 12px rgba(255, 107, 71, 0.08),
              0 1px 4px rgba(255, 107, 71, 0.06);
}

.sidebar-item.active::before {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%, rgba(255, 107, 71, 0.03) 100%);
  opacity: 1;
}

.sidebar-item-content {
  display: flex;
  align-items: center;
  gap: 12px;
  position: relative;
  z-index: 1;
}

.sidebar-item .sidebar-item-content span {
  background: linear-gradient(135deg, #475569 0%, #64748b 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  transition: all 0.25s ease;
}

.sidebar-item:hover .sidebar-item-content span {
  background: linear-gradient(135deg, #334155 0%, #475569 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.sidebar-item.active .sidebar-item-content span {
  background: linear-gradient(135deg, #ff5722 0%, #ff6b47 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 600;
}

/* Compact member profile cards */
.member-card {
  background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.member-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #ff6b47, #ff5722);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.member-card:hover {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-color: #cbd5e0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06), 0 2px 6px rgba(0, 0, 0, 0.04);
  transform: translateY(-1px);
}

.member-card:hover::before {
  opacity: 1;
}

.member-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.member-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.member-details h3 {
  margin: 0 0 2px 0;
  font-size: 14px;
  font-weight: 600;
  color: #1a202c;
}

.member-details p {
  margin: 0 0 1px 0;
  font-size: 12px;
  color: #718096;
}

.member-badges {
  display: flex;
  flex-direction: row;
  gap: 6px;
  align-items: center;
  flex-wrap: wrap;
}

.member-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #e2e8f0;
}

.member-stat {
  text-align: center;
  padding: 8px 6px;
  background: linear-gradient(135deg, #ffffff 0%, #f7fafc 100%);
  border-radius: 6px;
  border: 1px solid #e2e8f0;
  transition: all 0.2s ease;
}

.member-stat:hover {
  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
  border-color: #cbd5e0;
}

.member-stat-label {
  display: block;
  font-size: 10px;
  color: #718096;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 2px;
}

.member-stat-value {
  display: block;
  font-size: 14px;
  font-weight: 700;
  color: #2d3748;
}

/* Clean person avatar with orange glow */
.person-avatar {
  background: linear-gradient(135deg, #ffffff 0%, #f7fafc 100%) !important;
  border: 2px solid #ff5722 !important;
  box-shadow: 0 0 0 3px rgba(255, 87, 34, 0.1), 0 2px 8px rgba(255, 87, 34, 0.15) !important;
  color: #ff5722 !important;
}

/* Floating chat dialog */
.chat-dialog-overlay {
  position: fixed;
  bottom: 24px;
  right: 24px;
  z-index: 1000;
}

.chat-dialog-content {
  width: 400px;
  height: 600px;
  background: linear-gradient(135deg, #ffffff 0%, #fff8f6 100%);
  border-radius: 16px;
  border: 1px solid #e8e8e8;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15), 0 8px 24px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.chat-dialog-header {
  padding: 20px 24px;
  background: linear-gradient(135deg, #ff6b47 0%, #ff5722 100%);
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chat-dialog-body {
  flex: 1;
  padding: 24px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  background: linear-gradient(135deg, #fafbfc 0%, #fff8f6 100%);
}

.chat-dialog-footer {
  padding: 20px 24px;
  background: white;
  border-top: 1px solid #f0f0f0;
}

.chat-trigger-button {
  position: fixed;
  bottom: 24px;
  right: 24px;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, #ff6b47 0%, #ff5722 100%);
  border: none;
  box-shadow: 0 8px 24px rgba(255, 107, 71, 0.3), 0 4px 12px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  transition: all 0.3s ease;
  z-index: 999;
}

.chat-trigger-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 32px rgba(255, 107, 71, 0.4), 0 6px 16px rgba(0, 0, 0, 0.15);
}

/* Custom AI logo with depth */
.ai-logo-container {
  position: relative;
  width: 48px;
  height: 48px;
  margin-bottom: 16px;
}

.ai-logo-grid {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(rgba(255, 107, 71, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 107, 71, 0.1) 1px, transparent 1px);
  background-size: 8px 8px;
  border-radius: 12px;
}

.ai-logo-main {
  position: relative;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #ff6b47 0%, #ff5722 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 16px rgba(255, 107, 71, 0.2);
}

.ai-logo-strokes {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 24px;
  height: 24px;
}

.ai-stroke {
  position: absolute;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 1px;
}

.ai-stroke-1 {
  width: 16px;
  height: 2px;
  top: 6px;
  left: 4px;
}

.ai-stroke-2 {
  width: 12px;
  height: 1.5px;
  top: 10px;
  left: 6px;
}

.ai-stroke-3 {
  width: 8px;
  height: 1px;
  top: 13px;
  left: 8px;
}

.ai-stroke-4 {
  width: 4px;
  height: 0.5px;
  top: 15px;
  left: 10px;
}

.sidebar-separator {
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, #e2e8f0 20%, #e2e8f0 80%, transparent 100%);
  margin: 12px 20px;
}

/* User profile section */
.user-profile-section {
  padding: 24px 20px;
  background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
  border-bottom: 1px solid #e2e8f0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.02);
}

.user-avatar-container {
  position: relative;
  display: inline-block;
  flex-shrink: 0;
}

.user-status-dot {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 12px;
  height: 12px;
  background: #22c55e;
  border: 2px solid white;
  border-radius: 50%;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.user-info {
  flex: 1;
  min-width: 0;
}

.user-name {
  color: #1a202c !important;
  font-size: 15px !important;
  font-weight: 600 !important;
  line-height: 1.3;
  margin-bottom: 6px !important;
  display: block;
}

.user-role {
  color: #64748b !important;
  font-size: 12px !important;
  font-weight: 500 !important;
  line-height: 1.2;
  margin-bottom: 4px !important;
  display: block;
}

.user-email {
  color: #94a3b8 !important;
  font-size: 11px !important;
  font-weight: 400 !important;
  line-height: 1.2;
  display: block;
}

/* Logo styling */
.logo-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.logo-image {
  width: 24px;
  height: 24px;
  border-radius: 4px;
}
