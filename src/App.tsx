import { useState } from 'react'
import {
  Box,
  Flex,
  Text,
  Button,
  Card,
  Table,
  Badge,
  Progress,
  TextField,
  TextArea,
  DataList,
  Tooltip,
  Separator,
  Avatar,
  IconButton,
  Heading,
  Dialog
} from '@radix-ui/themes'
import {
  HomeIcon,
  VideoIcon,
  PersonIcon,
  ClockIcon,
  FileTextIcon,
  PlusIcon,
  MagnifyingGlassIcon,
  ChatBubbleIcon,
  InfoCircledIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  DotsHorizontalIcon,
  StarIcon,
  LightningBoltIcon,
  RocketIcon,
  GearIcon,
  QuestionMarkCircledIcon,
  ReaderIcon,
  GroupIcon,
  BarChartIcon,
  ActivityLogIcon,
  Cross2Icon,
  MinusIcon,
  PaperPlaneIcon
} from '@radix-ui/react-icons'

function App() {
  const [activeTab, setActiveTab] = useState('dashboard')

  return (
    <Flex style={{ minHeight: '100vh' }}>
      {/* Clean Sidebar */}
      <Box className="sidebar">
        {/* User Profile Section */}
        <Box className="user-profile-section">
          <Flex align="center" gap="3">
            <Box className="user-avatar-container">
              <PersonIcon
                style={{
                  width: '44px',
                  height: '44px',
                  padding: '10px',
                  borderRadius: '50%',
                  background: '#f8fafc',
                  border: '2px solid #e2e8f0',
                  color: '#64748b'
                }}
              />
              <Box className="user-status-dot"></Box>
            </Box>
            <Box className="user-info">
              <Text size="3" weight="bold" className="user-name">
                Cale Lane
              </Text>
              <Text size="1" className="user-role">
                Sales Manager
              </Text>
              <Text size="1" className="user-email">
                <EMAIL>
              </Text>
            </Box>
          </Flex>
        </Box>

        {/* Dashboard Section */}
        <Box className="sidebar-section">
          <Box className="sidebar-section-header">DASHBOARD</Box>
          <Box className="sidebar-nav">
            <button
              className={`sidebar-item ${activeTab === 'dashboard' ? 'active' : ''}`}
              onClick={() => setActiveTab('dashboard')}
            >
              <Box className="sidebar-item-content">
                <HomeIcon style={{ width: '18px', height: '18px', strokeWidth: '2' }} />
                <Text size="2">Overview</Text>
              </Box>
            </button>
            <button
              className={`sidebar-item ${activeTab === 'training' ? 'active' : ''}`}
              onClick={() => setActiveTab('training')}
            >
              <Box className="sidebar-item-content">
                <VideoIcon style={{ width: '18px', height: '18px', strokeWidth: '2' }} />
                <Text size="2">Sales Training</Text>
              </Box>
            </button>
            <button
              className={`sidebar-item ${activeTab === 'history' ? 'active' : ''}`}
              onClick={() => setActiveTab('history')}
            >
              <Box className="sidebar-item-content">
                <ClockIcon style={{ width: '18px', height: '18px', strokeWidth: '2' }} />
                <Text size="2">History</Text>
              </Box>
            </button>
            <button
              className={`sidebar-item ${activeTab === 'onboarding' ? 'active' : ''}`}
              onClick={() => setActiveTab('onboarding')}
            >
              <Box className="sidebar-item-content">
                <ReaderIcon style={{ width: '18px', height: '18px', strokeWidth: '2' }} />
                <Text size="2">Onboarding</Text>
              </Box>
            </button>
          </Box>
        </Box>

        <Box className="sidebar-separator"></Box>

        {/* Manage Section */}
        <Box className="sidebar-section">
          <Box className="sidebar-section-header">MANAGE</Box>
          <Box className="sidebar-nav">
            <button
              className={`sidebar-item ${activeTab === 'teams' ? 'active' : ''}`}
              onClick={() => setActiveTab('teams')}
            >
              <Box className="sidebar-item-content">
                <GroupIcon style={{ width: '18px', height: '18px', strokeWidth: '2' }} />
                <Text size="2">Teams</Text>
              </Box>
            </button>
            <button
              className={`sidebar-item ${activeTab === 'billing' ? 'active' : ''}`}
              onClick={() => setActiveTab('billing')}
            >
              <Box className="sidebar-item-content">
                <FileTextIcon style={{ width: '18px', height: '18px', strokeWidth: '2' }} />
                <Text size="2">Billing</Text>
              </Box>
            </button>
          </Box>
        </Box>

        <Box className="sidebar-separator"></Box>

        {/* System Section */}
        <Box className="sidebar-section">
          <Box className="sidebar-section-header">SYSTEM</Box>
          <Box className="sidebar-nav">
            <button
              className={`sidebar-item ${activeTab === 'admin' ? 'active' : ''}`}
              onClick={() => setActiveTab('admin')}
            >
              <Box className="sidebar-item-content">
                <GearIcon style={{ width: '18px', height: '18px', strokeWidth: '2' }} />
                <Text size="2">Admin</Text>
              </Box>
            </button>
          </Box>
        </Box>
      </Box>

      {/* Main Content */}
      <Box className="main-content">
        <Box className="dashboard-content">
          {/* Main Dashboard */}
          <Box className="dashboard-main">
            {activeTab === 'dashboard' && <Dashboard />}
            {activeTab === 'training' && <ComponentShowcase title="Sales Training" />}
            {activeTab === 'onboarding' && <ComponentShowcase title="Onboarding" />}
            {activeTab === 'history' && <ComponentShowcase title="History" />}
            {activeTab === 'teams' && <ComponentShowcase title="Teams" />}
            {activeTab === 'billing' && <ComponentShowcase title="Billing" />}
            {activeTab === 'admin' && <ComponentShowcase title="Admin" />}
          </Box>

          {/* Kendo AI Chat Section */}
          <Box className="ai-chat-section">
            <Box className="ai-chat-container">
              <Box className="ai-chat-header">
                <Flex justify="between" align="center">
                  <Flex align="center" gap="3">
                    <Text size="4" weight="bold" style={{ color: '#1a202c' }}>
                      Kendo AI
                    </Text>
                    <Text size="2" color="gray">Your Sales Assistant</Text>
                  </Flex>
                  <button className="classic-button">
                    <Flex align="center" gap="1">
                      <PlusIcon style={{ width: '12px', height: '12px' }} />
                      New Chat
                    </Flex>
                  </button>
                </Flex>
              </Box>

              <Box className="ai-chat-content">
                <Text size="6" weight="bold" style={{
                  marginBottom: '16px',
                  color: '#1a202c',
                  lineHeight: '1.2'
                }}>
                  How can I help you today?
                </Text>

                <Text size="3" style={{
                  marginBottom: '32px',
                  lineHeight: '1.6',
                  maxWidth: '380px',
                  color: '#4a5568'
                }}>
                  I can help you analyze sales data, track team performance,
                  and provide insights to improve your conversion rates.
                </Text>

                <Box style={{
                  display: 'grid',
                  gridTemplateColumns: '1fr 1fr',
                  gap: '12px',
                  width: '100%',
                  maxWidth: '380px'
                }}>
                  <button className="suggested-question">
                    Who took the most calls today?
                  </button>
                  <button className="suggested-question">
                    Show conversion rates
                  </button>
                  <button className="suggested-question">
                    Subscription status
                  </button>
                  <button className="suggested-question">
                    Generate report
                  </button>
                </Box>
              </Box>

              <Box className="ai-chat-input-area">
                <Box className="chat-input-container">
                  <div className="chat-placeholder-text">Ask Kendo anything...</div>
                  <textarea
                    className="chat-input"
                    placeholder=""
                    rows={1}
                  />
                  <button className="chat-send-button">
                    <PaperPlaneIcon style={{ width: '12px', height: '12px' }} />
                  </button>
                </Box>
                <Text size="1" style={{
                  marginTop: '12px',
                  textAlign: 'center',
                  color: '#9ca3af',
                  fontSize: '11px'
                }}>
                  Kendo AI can make mistakes. Consider checking important information.
                </Text>
              </Box>
            </Box>
          </Box>
        </Box>
      </Box>
    </Flex>
  )
}



// Professional Dashboard Component
function Dashboard() {
  const [searchTerm, setSearchTerm] = useState('')

  const teamMembers = [
    { name: 'Cale Lane', email: '<EMAIL>', date: 'July 25, 2024', status: 'Call in Progress', outcome: 'No Prospect Calls', statusColor: 'violet', outcomeColor: 'red', calls: 12, deals: 3, score: 85 },
    { name: 'Sarah Chen', email: '<EMAIL>', date: 'July 25, 2024', status: 'Completed', outcome: 'Qualified Lead', statusColor: 'green', outcomeColor: 'green', calls: 8, deals: 5, score: 92 },
    { name: 'Mike Johnson', email: '<EMAIL>', date: 'July 24, 2024', status: 'Follow-up Needed', outcome: 'Interested', statusColor: 'orange', outcomeColor: 'blue', calls: 15, deals: 2, score: 78 },
    { name: 'Emma Davis', email: '<EMAIL>', date: 'July 24, 2024', status: 'Completed', outcome: 'Closed Deal', statusColor: 'green', outcomeColor: 'green', calls: 6, deals: 4, score: 88 },
    { name: 'Alex Rodriguez', email: '<EMAIL>', date: 'July 23, 2024', status: 'Scheduled', outcome: 'Pending', statusColor: 'blue', outcomeColor: 'gray', calls: 9, deals: 1, score: 73 },
  ]

  const filteredMembers = teamMembers.filter(member =>
    member.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    member.email.toLowerCase().includes(searchTerm.toLowerCase())
  )

  return (
    <Box>
        {/* Clean Header */}
        <Box className="dashboard-header">
          <Flex justify="between" align="start">
            <Box>
              <h1 className="dashboard-title">Dashboard</h1>
              <p className="dashboard-subtitle">
                Welcome back, Cale! Here's what's happening with your sales team.
              </p>
            </Box>
            <Flex gap="2">
              <button
                className="classic-button"
                style={{
                  display: 'inline-flex',
                  alignItems: 'center',
                  gap: '6px',
                  whiteSpace: 'nowrap',
                  minWidth: 'auto',
                  width: 'auto'
                }}
              >
                <PlusIcon style={{ width: '12px', height: '12px' }} />
                Add Member
              </button>
              <button
                className="classic-button primary"
                style={{
                  whiteSpace: 'nowrap',
                  minWidth: 'auto',
                  width: 'auto'
                }}
              >
                Export
              </button>
            </Flex>
          </Flex>
        </Box>

        {/* Compact Metrics Cards */}
        <Flex gap="4" style={{ marginBottom: '24px' }}>
          <Card className="metrics-card" style={{ flex: 1 }}>
            <div className="metrics-label">Total Leads</div>
            <Flex align="center" gap="2" style={{ marginBottom: '6px' }}>
              <span className="metrics-value">27</span>
              <Flex align="center" gap="1">
                <ArrowUpIcon style={{ color: '#38a169', width: '12px', height: '12px' }} />
                <Badge color="green" variant="soft" size="1">+12%</Badge>
              </Flex>
            </Flex>
            <Text size="1" style={{ color: '#718096' }}>vs last month</Text>
          </Card>

          <Card className="metrics-card" style={{ flex: 1 }}>
            <div className="metrics-label">Avg Call Score</div>
            <Flex align="center" gap="2" style={{ marginBottom: '8px' }}>
              <span className="metrics-value">77%</span>
              <Flex align="center" gap="1">
                <ArrowUpIcon style={{ color: '#38a169', width: '12px', height: '12px' }} />
                <Badge color="green" variant="soft" size="1">+5%</Badge>
              </Flex>
            </Flex>
            <Progress value={77} style={{ height: '4px' }} />
          </Card>

          <Card className="metrics-card" style={{ flex: 1 }}>
            <div className="metrics-label">Active Calls</div>
            <Flex align="center" gap="2" style={{ marginBottom: '6px' }}>
              <span className="metrics-value">8</span>
              <Badge color="orange" variant="soft" size="1">Live</Badge>
            </Flex>
            <Text size="1" style={{ color: '#718096' }}>across 5 team members</Text>
          </Card>
        </Flex>

        {/* Compact Team Members */}
        <Box>
          <Flex justify="between" align="center" style={{ marginBottom: '16px' }}>
            <Box>
              <h2 className="section-title">Recent Activity</h2>
              <p className="section-subtitle">Track your team's latest call performance</p>
            </Box>
            <Flex gap="2">
              <TextField.Root
                placeholder="Search members..."
                size="2"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                style={{ width: '240px', height: '36px' }}
              >
                <TextField.Slot side="left">
                  <MagnifyingGlassIcon />
                </TextField.Slot>
              </TextField.Root>
            </Flex>
          </Flex>

          {/* Premium Member Cards */}
          <Box>
            {filteredMembers.map((member, i) => (
              <Box key={i} className="member-card">
                <Box className="member-header">
                  <Box className="member-info">
                    <Box style={{ position: 'relative' }}>
                      <PersonIcon
                        style={{
                          width: '36px',
                          height: '36px',
                          padding: '8px',
                          borderRadius: '50%'
                        }}
                        className="person-avatar"
                      />
                    </Box>
                    <Box className="member-details">
                      <h3>{member.name}</h3>
                      <p>{member.email}</p>
                      <p>Last active: {member.date}</p>
                    </Box>
                  </Box>
                  <Box className="member-badges">
                    <Badge color={member.statusColor as any} variant="soft" size="1">
                      {member.status}
                    </Badge>
                    <Badge color={member.outcomeColor as any} variant="soft" size="1">
                      {member.outcome}
                    </Badge>
                  </Box>
                </Box>

                <Box className="member-stats">
                  <Box className="member-stat">
                    <span className="member-stat-label">Calls</span>
                    <span className="member-stat-value">{member.calls}</span>
                  </Box>
                  <Box className="member-stat">
                    <span className="member-stat-label">Deals</span>
                    <span className="member-stat-value">{member.deals}</span>
                  </Box>
                  <Box className="member-stat">
                    <span className="member-stat-label">Score</span>
                    <span
                      className="member-stat-value"
                      style={{
                        color: member.score >= 85 ? '#38a169' : member.score >= 75 ? '#d69e2e' : '#e53e3e'
                      }}
                    >
                      {member.score}%
                    </span>
                  </Box>
                </Box>
              </Box>
            ))}
          </Box>
        </Box>
    </Box>
  )
}

// Enhanced Component Showcase for other tabs
function ComponentShowcase({ title }: { title: string }) {
  return (
    <Box>
      {/* Professional Header */}
      <Flex justify="between" align="center" style={{ marginBottom: '32px' }}>
        <Box>
          <Heading size="7" weight="bold" style={{ marginBottom: '4px' }}>
            {title}
          </Heading>
          <Text size="3" color="gray">
            Explore and interact with our design system components
          </Text>
        </Box>
        <Tooltip content="View component documentation">
          <IconButton variant="outline">
            <QuestionMarkCircledIcon />
          </IconButton>
        </Tooltip>
      </Flex>

      <Flex direction="column" gap="8">
        {/* Classic Buttons Section */}
        <Card className="metrics-card">
          <Flex justify="between" align="center" style={{ marginBottom: '20px' }}>
            <Box>
              <Text size="4" weight="bold" style={{ marginBottom: '4px' }}>
                Classic Buttons
              </Text>
              <Text size="2" color="gray">
                Professional buttons with inner shadows and hover effects
              </Text>
            </Box>
            <Tooltip content="These buttons have classic inner shadow styling">
              <InfoCircledIcon style={{ color: '#999' }} />
            </Tooltip>
          </Flex>
          <Flex gap="3" wrap="wrap" style={{ marginBottom: '16px' }}>
            <button className="classic-button primary">Primary Action</button>
            <button className="classic-button">Secondary Action</button>
            <button className="flat-button">Flat Button</button>
            <Tooltip content="This is a dashed button style">
              <button style={{
                background: 'transparent',
                border: '2px dashed #ff6b47',
                borderRadius: '6px',
                padding: '8px 16px',
                color: '#ff5722',
                cursor: 'pointer',
                fontSize: '14px',
                fontWeight: '500'
              }}>
                Dashed Button
              </button>
            </Tooltip>
          </Flex>
          <Separator style={{ margin: '16px 0' }} />
          <Text size="2" color="gray">
            💡 Tip: Hover over buttons to see the inner shadow effects and smooth transitions
          </Text>
        </Card>

        {/* Form Elements with Enhanced Styling */}
        <Card className="metrics-card">
          <Flex justify="between" align="center" style={{ marginBottom: '20px' }}>
            <Box>
              <Text size="4" weight="bold" style={{ marginBottom: '4px' }}>
                Form Elements
              </Text>
              <Text size="2" color="gray">
                Input fields with professional styling and validation states
              </Text>
            </Box>
          </Flex>
          <Flex direction="column" gap="4" style={{ maxWidth: '500px' }}>
            <Box>
              <Text size="2" weight="medium" style={{ marginBottom: '8px' }}>
                Name *
              </Text>
              <TextField.Root
                placeholder="Enter your full name"
                style={{
                  background: 'white',
                  border: '1px solid #e0e0e0',
                  borderRadius: '8px'
                }}
              />
            </Box>
            <Box>
              <Text size="2" weight="medium" style={{ marginBottom: '8px' }}>
                Message
              </Text>
              <TextArea
                placeholder="Tell us about your sales goals..."
                style={{
                  background: 'white',
                  border: '1px solid #e0e0e0',
                  borderRadius: '8px',
                  minHeight: '100px'
                }}
              />
            </Box>
            <Flex gap="2">
              <button className="classic-button primary">Submit</button>
              <button className="classic-button">Cancel</button>
            </Flex>
          </Flex>
        </Card>

        {/* Progress & Status with Creative Elements */}
        <Card className="metrics-card">
          <Text size="4" weight="bold" style={{ marginBottom: '20px' }}>
            Progress & Status Indicators
          </Text>
          <Flex direction="column" gap="6">
            <Box>
              <Flex justify="between" align="center" style={{ marginBottom: '8px' }}>
                <Text size="2" weight="medium">Sales Target Progress</Text>
                <Text size="2" color="gray">85% complete</Text>
              </Flex>
              <Progress value={85} style={{ height: '8px' }} />
            </Box>

            <Box>
              <Text size="3" weight="medium" style={{ marginBottom: '12px' }}>
                Team Status Overview
              </Text>
              <Flex gap="2" wrap="wrap">
                <Tooltip content="Team member is currently active">
                  <Badge color="green" variant="soft" style={{ padding: '4px 8px' }}>
                    🟢 Active (12)
                  </Badge>
                </Tooltip>
                <Tooltip content="Team member is on a call">
                  <Badge color="orange" variant="soft" style={{ padding: '4px 8px' }}>
                    📞 On Call (3)
                  </Badge>
                </Tooltip>
                <Tooltip content="Team member is in training">
                  <Badge color="blue" variant="soft" style={{ padding: '4px 8px' }}>
                    📚 Training (2)
                  </Badge>
                </Tooltip>
                <Tooltip content="Team member is offline">
                  <Badge color="gray" variant="soft" style={{ padding: '4px 8px' }}>
                    ⚫ Offline (1)
                  </Badge>
                </Tooltip>
              </Flex>
            </Box>
          </Flex>
        </Card>

        {/* Enhanced Data List */}
        <Card className="metrics-card">
          <Text size="4" weight="bold" style={{ marginBottom: '20px' }}>
            Data Overview
          </Text>
          <DataList.Root>
            <DataList.Item>
              <DataList.Label>
                <Flex align="center" gap="2">
                  <PersonIcon style={{ color: '#ff5722' }} />
                  Account Manager
                </Flex>
              </DataList.Label>
              <DataList.Value>
                <Flex align="center" gap="2">
                  Cale Lane
                  <Badge color="green" variant="soft" size="1">Pro</Badge>
                </Flex>
              </DataList.Value>
            </DataList.Item>
            <DataList.Item>
              <DataList.Label>
                <Flex align="center" gap="2">
                  <ChatBubbleIcon style={{ color: '#ff5722' }} />
                  Contact Email
                </Flex>
              </DataList.Label>
              <DataList.Value><EMAIL></DataList.Value>
            </DataList.Item>
            <DataList.Item>
              <DataList.Label>
                <Flex align="center" gap="2">
                  <StarIcon style={{ color: '#ff5722' }} />
                  Performance Rating
                </Flex>
              </DataList.Label>
              <DataList.Value>
                <Flex align="center" gap="2">
                  4.8/5.0
                  <Progress value={96} style={{ width: '60px', height: '4px' }} />
                </Flex>
              </DataList.Value>
            </DataList.Item>
            <DataList.Item>
              <DataList.Label>
                <Flex align="center" gap="2">
                  <RocketIcon style={{ color: '#ff5722' }} />
                  Team Role
                </Flex>
              </DataList.Label>
              <DataList.Value>
                <Badge color="tomato" variant="soft">Sales Manager</Badge>
              </DataList.Value>
            </DataList.Item>
          </DataList.Root>
        </Card>

        {/* Creative Info Tips Section */}
        <Card className="metrics-card">
          <Text size="4" weight="bold" style={{ marginBottom: '20px' }}>
            Interactive Elements & Tips
          </Text>
          <Flex direction="column" gap="4">
            <Flex align="center" gap="3">
              <Tooltip content="This shows real-time sales data updates">
                <Box style={{
                  padding: '12px',
                  background: 'linear-gradient(135deg, #fff5f5 0%, #ffe0db 100%)',
                  borderRadius: '8px',
                  border: '1px solid #ffcccb'
                }}>
                  <LightningBoltIcon style={{ color: '#ff5722' }} />
                </Box>
              </Tooltip>
              <Box>
                <Text size="2" weight="medium">Real-time Updates</Text>
                <Text size="1" color="gray">Hover the icon for more info</Text>
              </Box>
            </Flex>

            <Flex align="center" gap="3">
              <Tooltip content="AI-powered insights help optimize your sales process">
                <Box style={{
                  padding: '12px',
                  background: 'linear-gradient(135deg, #fff5f5 0%, #ffe0db 100%)',
                  borderRadius: '8px',
                  border: '1px solid #ffcccb'
                }}>
                  <RocketIcon style={{ color: '#ff5722' }} />
                </Box>
              </Tooltip>
              <Box>
                <Text size="2" weight="medium">AI Insights</Text>
                <Text size="1" color="gray">Smart recommendations</Text>
              </Box>
            </Flex>
          </Flex>
        </Card>
      </Flex>
    </Box>
  )
}

export default App
